import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'
import { z } from 'zod'
import { getProductPrice, getPackagePrice, type UserRole } from '../../utils/pricing'
import { validateInventoryForOrder } from '../../utils/inventory'
import { validateOrderPricing } from '../../utils/pricing-validation'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Order creation schema
const createOrderSchema = z.object({
  productId: z.string().uuid('Invalid product ID'),
  packageId: z.string().uuid('Invalid package ID').nullable().optional(),
  quantity: z.number().int().min(1).max(100),
  customData: z.record(z.any()).optional(),
  paymentMethod: z.enum(['wallet', 'external']).default('wallet'),
  currencyCode: z.string().length(3, 'Currency code must be 3 characters'),
  isStandaloneProduct: z.boolean().optional().default(false)
}).refine((data) => {
  // If standalone product, packageId should be null/undefined
  // If not standalone, packageId is required
  if (data.isStandaloneProduct) {
    return !data.packageId
  } else {
    return !!data.packageId
  }
}, {
  message: "Package ID is required for non-standalone products",
  path: ["packageId"]
})

// GET /api/orders - Get user's orders with pagination
export async function GET(request: NextRequest) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 50)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()

    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        error: 'Unauthorized'
      }, { status: 401 })
    }

    // Parse pagination and filter parameters
    const url = new URL(request.url)
    const page = Math.max(1, parseInt(url.searchParams.get('page') || '1'))
    const limit = Math.min(50, Math.max(1, parseInt(url.searchParams.get('limit') || '10')))
    const offset = (page - 1) * limit

    // Filter parameters
    const statusFilter = url.searchParams.get('status')
    const searchTerm = url.searchParams.get('search')
    const dateFilter = url.searchParams.get('date')

    // Get user's tenant and role
    console.log('Orders API - Fetching user profile for user ID:', user.id)
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('tenant_id, name, email, role')
      .eq('id', user.id)
      .single()

    console.log('Orders API - Profile:', profile, 'Error:', profileError)

    if (!profile) {
      console.log('Orders API - No user profile found')
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Determine query filters based on user role
    const isAdmin = profile.role === 'admin'
    const isWorker = profile.role === 'worker'
    const canViewAllOrders = isAdmin || isWorker

    // Get total count of orders for pagination
    let countQuery = supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', profile.tenant_id)

    // Regular users can only see their own orders
    if (!canViewAllOrders) {
      countQuery = countQuery.eq('user_id', user.id)
    }

    // Apply filters to count query
    if (statusFilter && ['pending', 'completed', 'failed'].includes(statusFilter)) {
      countQuery = countQuery.eq('status', statusFilter)
    }

    if (dateFilter) {
      const now = new Date()
      let startDate: Date

      switch (dateFilter) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(0) // No filter
      }

      if (dateFilter !== 'all') {
        countQuery = countQuery.gte('created_at', startDate.toISOString())
      }
    }

    const { count: totalOrders, error: countError } = await countQuery

    if (countError) {
      console.error('Orders API - Count error:', countError)
      return NextResponse.json({ error: 'Failed to count orders' }, { status: 500 })
    }

    // Get orders with product and package details (paginated)
    let ordersQuery = supabase
      .from('orders')
      .select(`
        id,
        amount,
        status,
        custom_data,
        created_at,
        updated_at,
        worker_id,
        worker_action,
        worker_action_at,
        products (
          id,
          title,
          slug,
          cover_image
        ),
        packages (
          id,
          name,
          price,
          image
        ),
        user_profiles!orders_user_id_fkey (
          id,
          name,
          email
        ),
        worker_profiles:user_profiles!orders_worker_id_fkey (
          id,
          name,
          email,
          role
        )
      `)
      .eq('tenant_id', profile.tenant_id)

    // Regular users can only see their own orders
    if (!canViewAllOrders) {
      ordersQuery = ordersQuery.eq('user_id', user.id)
    }

    // Apply filters to main query
    if (statusFilter && ['pending', 'completed', 'failed'].includes(statusFilter)) {
      ordersQuery = ordersQuery.eq('status', statusFilter)
    }

    if (dateFilter) {
      const now = new Date()
      let startDate: Date

      switch (dateFilter) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(0) // No filter
      }

      if (dateFilter !== 'all') {
        ordersQuery = ordersQuery.gte('created_at', startDate.toISOString())
      }
    }

    // Apply search filter (search in product title, package name, order ID)
    if (searchTerm) {
      // Note: For text search, we'll need to do client-side filtering since Supabase
      // doesn't support cross-table text search easily. For now, we'll search order IDs
      ordersQuery = ordersQuery.ilike('id', `%${searchTerm}%`)
    }

    const { data: orders, error } = await ordersQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Orders API - Database error:', error)
      return NextResponse.json({ error: 'Failed to fetch orders', details: error.message }, { status: 500 })
    }

    const totalPages = Math.ceil((totalOrders || 0) / limit)

    console.log('Orders API - Found orders:', orders?.length || 0, 'Total:', totalOrders, 'Page:', page)

    return NextResponse.json({
      success: true,
      orders: orders || [],
      pagination: {
        page,
        limit,
        total: totalOrders || 0,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Error in GET /api/orders:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST /api/orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { productId, packageId, quantity, customData, paymentMethod, currencyCode, isStandaloneProduct } = createOrderSchema.parse(body)

    // Get user's tenant and profile
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id, name, email, role')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Verify product exists and belongs to tenant
    const { data: product } = await supabase
      .from('products')
      .select('id, title, slug, original_price, user_price, discount_price, distributor_price, packages')
      .eq('id', productId)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    let packageData = null
    let totalAmountUSD = 0

    if (isStandaloneProduct) {
      // For standalone products, use product-level pricing
      const userRole = (profile.role as UserRole) || 'user'
      const productPrice = getProductPrice(product, userRole)

      if (!productPrice || productPrice <= 0) {
        return NextResponse.json({ error: 'Product pricing not available' }, { status: 400 })
      }

      totalAmountUSD = productPrice * quantity
    } else {
      // For package-based products, verify package exists
      const { data: pkg } = await supabase
        .from('packages')
        .select('id, name, original_price, user_price, discount_price, distributor_price, has_digital_codes, track_inventory, unlimited_stock, manual_quantity')
        .eq('id', packageId)
        .eq('product_id', productId)
        .single()

      if (!pkg) {
        return NextResponse.json({ error: 'Package not found' }, { status: 404 })
      }

      packageData = pkg

      // Apply role-based pricing for packages
      const userRole = (profile.role as UserRole) || 'user'
      const packagePrice = getPackagePrice(pkg, userRole)

      if (!packagePrice || packagePrice <= 0) {
        return NextResponse.json({ error: 'Package pricing not available' }, { status: 400 })
      }

      // Validate inventory availability
      const inventoryValidation = validateInventoryForOrder(pkg, quantity)
      if (!inventoryValidation.valid) {
        return NextResponse.json({ error: inventoryValidation.error }, { status: 400 })
      }

      totalAmountUSD = packagePrice * quantity
    }

    // Validate pricing consistency
    const userRole = (profile.role as UserRole) || 'user'
    const pricingValidation = validateOrderPricing(
      product,
      isStandaloneProduct ? null : packageId,
      userRole,
      quantity,
      totalAmountUSD
    )

    if (!pricingValidation.valid) {
      console.error('Pricing validation failed:', pricingValidation.errors)
      return NextResponse.json({
        error: 'Pricing validation failed',
        details: pricingValidation.errors
      }, { status: 400 })
    }

    // Log warnings if any
    if (pricingValidation.warnings.length > 0) {
      console.warn('Pricing validation warnings:', pricingValidation.warnings)
    }

    // Get currency exchange rate for conversion
    const { data: currency } = await supabase
      .from('currencies')
      .select('exchange_rate')
      .eq('code', currencyCode)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!currency) {
      return NextResponse.json({ error: 'Currency not supported' }, { status: 400 })
    }

    // Convert to user's selected currency
    const totalAmountInCurrency = totalAmountUSD * currency.exchange_rate

    // If paying with wallet, check and deduct balance
    if (paymentMethod === 'wallet') {
      // Get current balance
      const { data: balanceData } = await supabase
        .from('user_currency_balances')
        .select('balance')
        .eq('user_id', user.id)
        .eq('currency_code', currencyCode)
        .eq('tenant_id', profile.tenant_id)
        .single()

      const currentBalance = balanceData?.balance || 0

      if (currentBalance < totalAmountInCurrency) {
        return NextResponse.json({ 
          error: 'Insufficient balance',
          required: totalAmountInCurrency,
          available: currentBalance,
          currency: currencyCode
        }, { status: 400 })
      }

      // Deduct balance
      const newBalance = currentBalance - totalAmountInCurrency
      const { error: balanceError } = await supabase
        .from('user_currency_balances')
        .update({ 
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .eq('currency_code', currencyCode)
        .eq('tenant_id', profile.tenant_id)

      if (balanceError) {
        console.error('Error updating balance:', balanceError)
        return NextResponse.json({ error: 'Failed to process payment' }, { status: 500 })
      }

      // Log balance change
      const itemName = isStandaloneProduct ? product.title : `${product.title} - ${packageData?.name}`
      await supabase
        .from('balance_change_log')
        .insert({
          user_id: user.id,
          currency_code: currencyCode,
          amount_change: -totalAmountInCurrency,
          balance_before: currentBalance,
          balance_after: newBalance,
          change_type: 'purchase',
          notes: `Purchase: ${itemName} x${quantity}`,
          tenant_id: profile.tenant_id
        })
    }

    // Calculate earnings for this order
    const costPerUnit = isStandaloneProduct
      ? (product.original_price || 0)
      : (packageData?.original_price || 0)
    const totalCostUSD = costPerUnit * quantity
    const profitUSD = totalAmountUSD - totalCostUSD
    const profitMarginPercent = totalAmountUSD > 0 ? (profitUSD / totalAmountUSD) * 100 : 0

    // Create order with earnings data
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        user_id: user.id,
        product_id: productId,
        package_id: isStandaloneProduct ? null : packageId,
        amount: totalAmountUSD, // Revenue in USD
        status: paymentMethod === 'wallet' ? 'completed' : 'pending',
        custom_data: {
          ...customData,
          quantity,
          currency_code: currencyCode,
          amount_in_currency: totalAmountInCurrency,
          payment_method: paymentMethod,
          is_standalone_product: isStandaloneProduct,
          // Store earnings data
          cost_usd: totalCostUSD,
          profit_usd: profitUSD,
          profit_margin_percent: profitMarginPercent,
          exchange_rate_used: currency.exchange_rate,
          exchange_rate_timestamp: new Date().toISOString()
        },
        tenant_id: profile.tenant_id
      })
      .select()
      .single()

    if (orderError) {
      return NextResponse.json({ error: 'Failed to create order' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      order: {
        id: order.id,
        status: order.status,
        amount: totalAmountInCurrency,
        currency: currencyCode,
        product: product.title,
        package: packageData.name,
        quantity,
        createdAt: order.created_at
      }
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }
    
    console.error('Error in POST /api/orders:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
